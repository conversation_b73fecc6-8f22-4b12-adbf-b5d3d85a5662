package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/labstack/echo/v4"
	"google.golang.org/grpc/metadata"

	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/clients"
	grpc_handler "gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/handler/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/seeds"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/service"
	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/health"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/tracing"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic(fmt.Sprintf("failed to load config: %v", err))
	}

	logger := logging.New(cfg.Logging.Level, cfg.Logging.Format)
	logger.Infof("Starting service: %s v%s", cfg.Service.Name, cfg.Service.Version)

	tracer, err := tracing.New(cfg.Service.Name, cfg.Jaeger.Host, cfg.Jaeger.Port)
	if err != nil {
		logger.Fatalf("failed to initialize tracer: %v", err)
	}

	appMetrics := metrics.New(cfg.Service.Name)
	jwtManager := auth.NewJWTManager(&cfg.Auth)

	db, err := database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		logger.Fatalf("failed to connect to database: %v", err)
	}

	// Run GORM AutoMigrate
	autoMigrator := database.NewAutoMigrator(db, logger)
	models := []interface{}{
		&model.UserCredential{},
		&model.ServiceCredential{},
		&model.RefreshToken{},
		&model.Role{},
		&model.UserRole{},
	}

	if err := autoMigrator.AutoMigrateWithSeeds(models, seeds.SeedDefaultRoles); err != nil {
		logger.Fatalf("failed to run database migrations: %v", err)
	}
	redisClient := redis.NewClient(&cfg.Redis, logger, appMetrics)

	// Create user service client
	userClient, err := clients.NewUserClient(
		cfg.DownstreamServices.UserServiceAddr,
		&cfg.GRPC,
		logger,
		appMetrics,
		cfg.Service.ClientID,
		cfg.Service.ClientKey,
	)
	if err != nil {
		logger.Fatalf("Failed to create user service client: %v", err)
	}
	defer userClient.Close()

	repo := repository.NewAuthRepository(db, redisClient)
	svc := service.NewAuthService(repo, userClient, jwtManager, logger, cfg)

	healthChecker := health.NewHealthChecker()
	healthChecker.AddCheck("database", db.Health)
	healthChecker.AddCheck("redis", redisClient.Health)

	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	var wg sync.WaitGroup
	wg.Add(2)

	go func() {
		defer wg.Done()
		// Create custom auth function that handles both JWT and Bootstrap Token
		authFunc := createAuthFunc(jwtManager, cfg)
		startGRPCServer(ctx, cfg, logger, appMetrics, svc, authFunc)
	}()
	go func() {
		defer wg.Done()
		startHTTPServer(ctx, cfg, logger, healthChecker, appMetrics)
	}()

	wg.Wait()
	tracer.Close()
	db.Close()
	redisClient.Close()
	logger.Info("Shutdown complete")
}

func startGRPCServer(ctx context.Context, cfg *config.Config, logger *logging.Logger, metrics *metrics.Metrics, svc service.AuthService, authFunc func(context.Context) (context.Context, error)) {
	grpcServerWrapper := shared_grpc.NewServer(&cfg.GRPC, logger, metrics, authFunc)
	grpcHandler := grpc_handler.NewAuthServer(svc)
	proto_auth_v1.RegisterAuthServiceServer(grpcServerWrapper.Server, grpcHandler)

	go func() {
		if err := grpcServerWrapper.Start(); err != nil {
			logger.Errorf("gRPC server failed: %v", err)
		}
	}()

	<-ctx.Done()
	grpcServerWrapper.Stop()
}

// createAuthFunc creates a custom auth function that handles both JWT tokens and Bootstrap Token
func createAuthFunc(jwtManager *auth.JWTManager, cfg *config.Config) func(context.Context) (context.Context, error) {
	return func(ctx context.Context) (context.Context, error) {
		// Get metadata from context
		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			fmt.Printf("Debug: No metadata in context\n")
			return ctx, nil // Let it pass, will be handled by the service layer
		}

		// Check for Bootstrap Token first (for RegisterService method)
		bootstrapTokens := md.Get("bootstrap-token")
		fmt.Printf("Debug: Bootstrap tokens from metadata: %v\n", bootstrapTokens)
		fmt.Printf("Debug: Expected bootstrap token: '%s'\n", cfg.Auth.BootstrapToken)
		if len(bootstrapTokens) > 0 && bootstrapTokens[0] == cfg.Auth.BootstrapToken {
			// Valid Bootstrap Token, let it pass
			fmt.Printf("Debug: Bootstrap token matched, allowing request\n")
			return ctx, nil
		}

		// Fall back to JWT authentication
		fmt.Printf("Debug: No valid bootstrap token, falling back to JWT auth\n")
		return jwtManager.AuthFunc(ctx)
	}
}

func startHTTPServer(ctx context.Context, cfg *config.Config, logger *logging.Logger, healthChecker *health.HealthChecker, metrics *metrics.Metrics) {
	e := echo.New()
	e.GET("/health", healthChecker.HTTPHandler())
	e.GET(cfg.Metrics.Path, echo.WrapHandler(metrics.Handler()))

	addr := fmt.Sprintf(":%d", cfg.Service.Port)
	server := &http.Server{Addr: addr, Handler: e}

	logger.Infof("Starting operational HTTP server on %s", addr)

	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("HTTP server failed: %v", err)
		}
	}()

	<-ctx.Done()

	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Fatalf("HTTP server shutdown failed: %v", err)
	}
}
