#!/bin/bash

# Test script for CI/CD Service Registration Flow
# This script tests the complete service registration flow

set -e  # Exit on any error

echo "🧪 Testing CI/CD Service Registration Flow"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if auth service is running
echo "1. Checking if auth service is running..."
if curl -s http://localhost:8081/health > /dev/null; then
    print_status "Auth service is running"
else
    print_error "Auth service is not running. Please start it with 'docker-compose up -d'"
    exit 1
fi

# Build the CLI tool
echo "2. Building CLI tool..."
if make build-cli > /dev/null 2>&1; then
    print_status "CLI tool built successfully"
else
    print_error "Failed to build CLI tool"
    exit 1
fi

# Test service registration
echo "3. Testing service registration..."
SERVICE_NAME="test-service-$(date +%s)"  # Unique name to avoid conflicts

if make register-service SERVICE_NAME="$SERVICE_NAME" SERVICE_VERSION="1.0.0" DESCRIPTION="Test service for validation"; then
    print_status "Service registration completed"
else
    print_error "Service registration failed"
    exit 1
fi

# Check if credentials file was created
CREDS_FILE="${SERVICE_NAME}-credentials.env"
if [ -f "$CREDS_FILE" ]; then
    print_status "Credentials file created: $CREDS_FILE"
    
    # Show the contents (for testing purposes)
    echo "4. Generated credentials:"
    cat "$CREDS_FILE"
    
    # Clean up the test file
    rm "$CREDS_FILE"
    print_status "Test credentials file cleaned up"
else
    print_warning "Credentials file not found, but registration may have succeeded"
fi

# Test with different parameters
echo "5. Testing with custom parameters..."
SERVICE_NAME_2="advanced-test-service-$(date +%s)"

if make register-service SERVICE_NAME="$SERVICE_NAME_2" SERVICE_VERSION="2.1.0" DESCRIPTION="Advanced test service with custom parameters"; then
    print_status "Advanced service registration completed"
    
    # Clean up
    CREDS_FILE_2="${SERVICE_NAME_2}-credentials.env"
    if [ -f "$CREDS_FILE_2" ]; then
        rm "$CREDS_FILE_2"
    fi
else
    print_error "Advanced service registration failed"
    exit 1
fi

# Test error handling - try to register without bootstrap token
echo "6. Testing error handling..."
print_warning "This test should fail (testing error handling)..."

# Temporarily rename the config to simulate missing bootstrap token
if [ -f "config/config.yaml" ]; then
    mv config/config.yaml config/config.yaml.backup
    
    # Create a config without bootstrap token
    cat > config/config.yaml << 'EOF'
service:
  name: "auth-service"
  version: "1.0.0"
  environment: "development"
  port: 8080
  grpc_port: 50051

auth:
  jwt_secret: "ca4t3s5d6f7g8hj"
  jwt_expiration: "24h"
  # bootstrap_token is missing - this should cause an error

database:
  host: "postgres-auth"
  port: 5432
  user: "auth_service"
  password: "123456789"
  name: "auth_db"
  ssl_mode: "disable"
EOF

    # Try to register - this should fail
    if make register-service SERVICE_NAME="should-fail-service" 2>/dev/null; then
        print_error "Registration should have failed but didn't"
        # Restore config
        mv config/config.yaml.backup config/config.yaml
        exit 1
    else
        print_status "Error handling works correctly - registration failed as expected"
    fi
    
    # Restore original config
    mv config/config.yaml.backup config/config.yaml
fi

echo ""
echo "🎉 All tests passed!"
echo "The CI/CD Service Registration Flow is working correctly."
echo ""
echo "Summary of what was tested:"
echo "- Auth service connectivity"
echo "- CLI tool compilation"
echo "- Basic service registration"
echo "- Credentials file generation"
echo "- Advanced service registration with custom parameters"
echo "- Error handling for missing bootstrap token"
echo ""
echo "You can now use this flow in your CI/CD pipelines!"
