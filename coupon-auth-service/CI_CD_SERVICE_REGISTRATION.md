# CI/CD-Driven Service Registration Flow

This document describes the implementation of the CI/CD-driven service registration flow that allows services to automatically register themselves with the auth service during deployment.

## Overview

The service registration flow simulates a CI/CD pipeline where:
1. A service is built and deployed
2. During deployment, the service registers itself with the auth service using a Bootstrap Token
3. The auth service generates unique `client_id` and `client_key` credentials
4. These credentials are used by the service for subsequent service-to-service authentication

## Components

### 1. Bootstrap Token Configuration

The Bootstrap Token is a special authentication token used exclusively for service registration during CI/CD deployments.

**Configuration:**
- Added `bootstrap_token` field to `AuthConfig` struct
- Configured in `config/config.yaml` and environment variables
- Used to authenticate the `RegisterService` RPC call

**Files Modified:**
- `coupon-shared-libs/config/config.go` - Added `BootstrapToken` field
- `coupon-auth-service/config/config.yaml` - Added bootstrap token configuration
- `coupon-auth-service/.env.example` - Added `BOOTSTRAP_TOKEN` environment variable

### 2. Enhanced Authorization Logic

The auth service now supports Bootstrap Token authentication for the `RegisterService` method.

**Implementation:**
- Modified `authorizeService()` method in `auth_service.go`
- Special handling for `RegisterService` method to check for Bootstrap Token
- Falls back to regular service authentication if Bootstrap Token is not provided

**Authentication Flow:**
1. Check if method is public (no auth required)
2. For `RegisterService`: Check for valid Bootstrap Token in metadata
3. For other methods: Use regular client-id/client-key authentication

### 3. CLI Tool for Service Registration

A command-line tool (`cmd/cli/main.go`) that acts as a gRPC client to register services.

**Features:**
- Connects to auth service using Bootstrap Token
- Calls `RegisterService` RPC with service details
- Outputs generated credentials in multiple formats
- Saves credentials to a file for CI/CD consumption

**Usage:**
```bash
./auth-cli -service-name="user-service" -service-version="1.0.0" -description="User management service"
```

### 4. Makefile Integration

Added `register-service` target to simulate CI/CD pipeline behavior.

**Usage:**
```bash
make register-service SERVICE_NAME=user-service SERVICE_VERSION=1.0.0 DESCRIPTION="User management service"
```

**What it does:**
1. Builds the CLI tool
2. Runs service registration with provided parameters
3. Outputs credentials in CI/CD-friendly format
4. Saves credentials to a file

## Usage Examples

### Basic Service Registration

```bash
# Register a user service
make register-service SERVICE_NAME=user-service

# Register with specific version and description
make register-service SERVICE_NAME=order-service SERVICE_VERSION=2.1.0 DESCRIPTION="Order processing service"
```

### Expected Output

```
🚀 Simulating CI/CD Service Registration for user-service
==================================================
Registering service: user-service (version: 1.0.0)
Auth service address: localhost:50052

✅ Service registration successful!
Service ID: 123e4567-e89b-12d3-a456-426614174000
Client ID: 456e7890-e89b-12d3-a456-426614174001
Client Key: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6

# Environment variables for CI/CD:
export USER_SERVICE_CLIENT_ID=456e7890-e89b-12d3-a456-426614174001
export USER_SERVICE_CLIENT_KEY=a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6

📄 Credentials saved to: user-service-credentials.env
```

### Integration with Docker Compose

The generated credentials can be used in docker-compose.yml:

```yaml
services:
  user-service:
    build: .
    environment:
      - SERVICE_CLIENT_ID=${USER_SERVICE_CLIENT_ID}
      - SERVICE_CLIENT_KEY=${USER_SERVICE_CLIENT_KEY}
    # ... other configuration
```

## CI/CD Pipeline Integration

### GitHub Actions Example

```yaml
name: Deploy Service
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Register Service
        run: |
          make register-service SERVICE_NAME=user-service SERVICE_VERSION=${{ github.sha }}
          source user-service-credentials.env
          
      - name: Deploy with Credentials
        env:
          USER_SERVICE_CLIENT_ID: ${{ env.USER_SERVICE_CLIENT_ID }}
          USER_SERVICE_CLIENT_KEY: ${{ env.USER_SERVICE_CLIENT_KEY }}
        run: |
          docker-compose up -d
```

### GitLab CI Example

```yaml
deploy:
  stage: deploy
  script:
    - make register-service SERVICE_NAME=user-service SERVICE_VERSION=$CI_COMMIT_SHA
    - source user-service-credentials.env
    - export USER_SERVICE_CLIENT_ID USER_SERVICE_CLIENT_KEY
    - docker-compose up -d
```

## Security Considerations

1. **Bootstrap Token Security:**
   - Should be stored securely in CI/CD environment variables
   - Should be rotated regularly
   - Should only be accessible to authorized CI/CD pipelines

2. **Generated Credentials:**
   - Client keys are hashed before storage
   - Raw client keys are only shown once during registration
   - Credentials should be stored securely in deployment environment

3. **Network Security:**
   - gRPC communication should use TLS in production
   - Bootstrap token should be transmitted over secure channels

## Testing the Implementation

### Prerequisites

1. Start the auth service:
   ```bash
   cd coupon-auth-service
   docker-compose up -d
   ```

2. Ensure the service is healthy:
   ```bash
   curl http://localhost:8081/health
   ```

### Test Service Registration

```bash
# Test basic registration
make register-service SERVICE_NAME=test-service

# Test with all parameters
make register-service SERVICE_NAME=test-service SERVICE_VERSION=1.2.3 DESCRIPTION="Test service for validation"
```

### Verify Registration

The registered service should appear in the `service_credentials` table in the auth database.

## Troubleshooting

### Common Issues

1. **"Bootstrap token not configured"**
   - Ensure `BOOTSTRAP_TOKEN` is set in environment or config file

2. **"Failed to connect to auth service"**
   - Verify auth service is running on the specified address
   - Check network connectivity and firewall settings

3. **"Service registration failed: unauthorized"**
   - Verify Bootstrap Token is correct
   - Check auth service logs for detailed error messages

### Debug Mode

Enable debug logging in the auth service to see detailed authentication flow:

```yaml
logging:
  level: "debug"
```

## Future Enhancements

1. **Token Rotation:** Implement automatic Bootstrap Token rotation
2. **Service Discovery:** Integration with service discovery systems
3. **Credential Renewal:** Automatic credential renewal before expiration
4. **Audit Logging:** Enhanced logging for security auditing
5. **Multi-Environment:** Support for different environments (dev, staging, prod)
